// Top-level build file where you can add configuration options common to all sub-projects/modules.
import org.gradle.api.JavaVersion
buildscript {
    repositories {
        maven { url = uri("https://maven.aliyun.com/repository/public") }
        maven { url = uri("https://maven.aliyun.com/repository/google") }
        maven { url = uri("https://maven.aliyun.com/repository/gradle-plugin") }
        google()
        mavenCentral()
    }
}

plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.ksp) apply false
    alias(libs.plugins.spotless)
    alias(libs.plugins.gradle.versions)
    alias(libs.plugins.version.catalog.update)
//    alias(libs.plugins.hilt) apply false


}

apply("${project.rootDir}/buildscripts/toml-updater-config.gradle")


