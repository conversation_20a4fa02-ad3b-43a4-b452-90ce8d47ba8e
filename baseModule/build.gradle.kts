plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.ksp)
//    alias(libs.plugins.hilt)

}
val versionConfig = VersionConfig()
android {
    namespace = "com.huiver.wen.base.module"
    compileSdk = versionConfig.compileSdk

    defaultConfig {
        minSdk = versionConfig.minSdk
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        javaCompileOptions {
            annotationProcessorOptions {

                arguments ["AROUTER_MODULE_NAME"] = project.name
            }
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }


    compileOptions {

        sourceCompatibility = versionConfig.javaSDK
        targetCompatibility =  versionConfig.javaSDK
    }
    kotlinOptions {
        jvmTarget =  versionConfig.javaSDK.toString()
    }
    buildFeatures {
        buildConfig = true
        viewBinding = true
    }
}

dependencies {

    api(project(":libCommon"))
    api(libs.androidx.core.ktx)
    api(libs.androidx.appcompat)
    api(libs.material)
    api(libs.androidx.constraintlayout)
    api(libs.androidx.annotations)


//    api(libs.androidx.recyclerview)
    api(libs.kotlinx.coroutines.core)
    api(libs.kotlinx.coroutines.android)

    api(libs.navigation.ui.kt)
    api(libs.navigation.features)
    api(libs.navigation.fragment)

    api(libs.lifecycle.runtime)
    api(libs.lifecycle.extensions)
    api(libs.lifecycle.viewmodel.ktx)
    api(libs.lifecycle.viewmodel.savedstate)
    api(libs.lifecycle.viewmodel)
    api(libs.lifecycle.livedata)
    api(libs.viewbinding)

    api(libs.immersionbar.common)
    api(libs.immersionbar.ktx)
    api(libs.brvah)


    implementation(libs.squareup.retrofit)
    implementation(libs.squareup.retrofit.gson)
    implementation(libs.squareup.okhttp)
    implementation(libs.squareup.okhttp.interceptor)

//    implementation(libs.coil)
//    implementation(libs.coil.okhttp)
//    implementation(libs.coil.svg)
//    implementation(libs.coil.gif)
//    implementation(libs.coil.video)

    api(libs.androidx.room.ktx)
    api(libs.androidx.room.runtime)
    api(libs.androidx.room.paging)
    ksp(libs.androidx.room.compiler)
    api(libs.paging.runtime.kt)

//    api(libs.)

    //hilt
//    ksp(libs.hilt.compiler)
//    api(libs.hilt.android)




//    implementation(libs.hilt.lifecycle.viewmodel)
//    androidTestImplementation(libs.hilt.android.testing)
//    androidTestImplementation(libs.statusbarutil)


    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)

}