package com.huiver.wen.ui.brvah.adapter

import android.content.Context
import android.view.ViewGroup
import com.chad.library.adapter4.BaseQuickAdapter
import com.huiver.wen.base.ui.widget.ViewBindingHolder
import com.huiver.wen.databinding.ItemBrvahBinding
import com.huiver.wen.entities.BravhItem

class DemoQuickAdapter  :BaseQuickAdapter<BravhItem,ViewBindingHolder<ItemBrvahBinding>>(){


    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): ViewBindingHolder<ItemBrvahBinding> {


        return ViewBindingHolder(parent,ItemBrvahBinding::inflate)
    }

    override fun onBindViewHolder(
        holder: ViewBindingHolder<ItemBrvahBinding>,
        position: Int,
        item: BravhItem?
    ) {
        holder.binding.tvContent.text = item?.content
    }
}