package com.huiver.wen.ui.brvah

import android.view.LayoutInflater
import com.huiver.wen.base.ui.activity.BaseActivity
import com.huiver.wen.databinding.ActivityBrvahBinding
import com.huiver.wen.lib.common.LogUtil


class BRVAHDemoActivity :BaseActivity<ActivityBrvahBinding>() {


    override fun getViewBinding(inflater: LayoutInflater): ActivityBrvahBinding {
        return ActivityBrvahBinding.inflate(inflater)
    }

    override fun initData() {

    }

    override fun initView() {

    }

    override fun onObserver() {

    }

    override fun onRequest() {

    }
}