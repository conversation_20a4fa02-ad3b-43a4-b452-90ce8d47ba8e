package com.huiver.wen.ui.brvah.fragment

import android.view.LayoutInflater
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.huiver.wen.base.ui.fragment.BaseFragment
import com.huiver.wen.databinding.FragmentListBinding
import com.huiver.wen.ui.brvah.adapter.DemoQuickAdapter
import com.huiver.wen.ui.brvah.viewmodel.BrvahQuickDemoViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


class QuickAdapterFragment :BaseFragment<FragmentListBinding>(){


    private val demoQuickAdapter: DemoQuickAdapter by lazy { DemoQuickAdapter() }
    private val mViewModel: BrvahQuickDemoViewModel by viewModels()
    
    override fun getViewBinding(inflater: LayoutInflater): FragmentListBinding {
        return FragmentListBinding.inflate(inflater)
    }



    override fun initData() {

    }

    override fun initView() {
        mBinding.recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = demoQuickAdapter
        }

    }

    override fun onObserver() {
        mViewModel.mDemoLiveData.observe(this){
            demoQuickAdapter.addAll(it)
        }
    }

    override fun onRequest() {
        mViewModel.loadData()
    }
}