[versions]
agp = "8.10.1"
kotlin = "2.1.10"
coreKtx = "1.16.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.1"
material = "1.12.0"
gradle-versions = "0.51.0"
version-catalog-update = "0.8.4"
ksp = "2.1.10-1.0.30"
spotless = "6.25.0"
constraintlayout = "2.2.1"
hilt = "2.51.1"
annotationVersion = "1.9.1"
lifecycle = "2.9.1"
coil = "3.2.0"
room = "2.7.1"
navigation = "2.9.0"
flexbox = "3.0.0"
recyclerview = "1.4.0"
work = "2.10.1"
coroutines = "1.9.0"
statusbarutil = "1.5.1"
timber = "5.0.1"
bugly = "4.1.9.3"
arouterCompilerVersion = "1.2.2"
viewbinding = "8.10.1"
lifecycleExtensions = "2.2.0"
retrofit = "2.11.0"
okhttp = "4.9.3"

paging = "3.2.1"
okHttp = "4.12.0"
immersionbar = "3.2.2"
brvah = "4.1.7"
refreshLayout = "3.0.0-alpha"


[libraries]
#Android core
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
androidx-annotations = { group = "androidx.annotation", name = "annotation", version.ref = "annotationVersion" }
android-flexbox = { group = "com.google.android.flexbox", name = "flexbox", version.ref = "flexbox" }
androidx-recyclerview = { group = "androidx.recyclerview", name = "recyclerview", version.ref = "recyclerview" }
androix-work-runtime = { group = "androidx.work", name = "work-runtime-ktx", version.ref = "work" }
androix-work-testing = { group = "androidx.work", name = "work-testing", version.ref = "work" }
viewbinding = { group = "androidx.databinding", name = "viewbinding", version.ref = "viewbinding" }


#coroutines 写成
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "coroutines" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "coroutines" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "coroutines" }


#Retrofit
squareup-retrofit ={ group="com.squareup.retrofit2",name= "retrofit" , version.ref="retrofit"}
squareup-retrofit-gson ={ group="com.squareup.retrofit2",name= "converter-gson" , version.ref="retrofit"}

#OkHttp
squareup-okhttp=  { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okHttp" }
squareup-okhttp-interceptor =  { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okHttp" }

#Room
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
androidx-room-paging = { group = "androidx.room", name = "room-paging", version.ref = "room" }

#Navigation
navigation-fragment = { group = "androidx.navigation", name = "navigation-fragment-ktx", version.ref = "navigation" }
navigation-ui-kt = { group = "androidx.navigation", name = "navigation-ui-ktx", version.ref = "navigation" }
navigation-features = { group = "androidx.navigation", name = "navigation-dynamic-features-fragment", version.ref = "navigation" }

#lifecycle
lifecycle-runtime = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycle" }
lifecycle-extensions = { module = "androidx.lifecycle:lifecycle-extensions", version.ref = "lifecycleExtensions" }
lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
lifecycle-livedata = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycle" }
lifecycle-viewmodel-savedstate= { module = "androidx.lifecycle:lifecycle-viewmodel-savedstate", version.ref = "lifecycle" }
lifecycle-viewmodel = { group = "androidx.lifecycle", name = "lifecycle-viewmodel", version.ref = "lifecycle" }


paging-runtime-kt = { group ="androidx.paging", name="paging-runtime-ktx", version.ref="paging"}



#coil图片框架
coil = { group = "io.coil-kt", name = "coil", version.ref = "coil" }
coil-okhttp = { group = "io.coil-kt", name = "coil-network-okhttp", version.ref = "coil" }
coil-svg = { group = "io.coil-kt", name = "coil-svg", version.ref = "coil" }
coil-gif = { group = "io.coil-kt", name = "coil-gif", version.ref = "coil" }
coil-video = { group = "io.coil-kt", name = "coil-video", version.ref = "coil" }


#Android test
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }

#Hilt dependencies
#hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "hilt" }
#hilt-compiler = {  module = "com.google.dagger:hilt-compiler", version.ref = "hilt" }
#hilt-android-testing = { module = "com.google.dagger:hilt-android-testing", version.ref = "hilt" }

#immersionbar  https://github.com/gyf-dev/ImmersionBar
immersionbar-common={group ="com.geyifeng.immersionbar" ,name="immersionbar" ,version.ref = "immersionbar"}
immersionbar-ktx={group ="com.geyifeng.immersionbar" ,name="immersionbar-ktx" ,version.ref = "immersionbar"}



#Kotlin
kotlin-stdlib = { module = "org.jetbrains.kotlin:kotlin-stdlib", version.ref = "kotlin" }
kotlin-stdlib-jdk8 = { module = "org.jetbrains.kotlin:kotlin-stdlib-jdk8", version.ref = "kotlin" }

#git第三方框架
#statusbarutil ={module = "com.jaeger.statusbarutil:library",  version.ref = "statusbarutil" }
#日志库
timber = { group ="com.jakewharton.timber" ,name = "timber" ,version.ref = "timber" }
bulgy =  { group ="com.tencent.bugly" ,name = "crashreport" ,version.ref = "bugly" }


# https://github.com/CymChad/BaseRecyclerViewAdapterHelper
brvah = {group="io.github.cymchad", name="BaseRecyclerViewAdapterHelper4", version.ref="brvah"}

# https://github.com/scwang90/SmartRefreshLayout 下拉刷新

refresh-layout-kernel = { module ="io.github.scwang90:refresh-layout-kernel", name = "refresh-layout-kernel",version.ref = "refreshLayout"}#核心必须依赖
refresh-header-classics = {group ="io.github.scwang90", name = "refresh-header-classics",version.ref = "refreshLayout"}#经典刷新头
refresh-header-radar = {group ="io.github.scwang90", name = "refresh-header-radar",version.ref = "refreshLayout"}#雷达刷新头
refresh-header-falsify = {group ="io.github.scwang90", name = "refresh-header-falsify",version.ref = "refreshLayout"}#虚拟刷新头
refresh-header-material = {group ="io.github.scwang90", name = "refresh-header-material",version.ref = "refreshLayout"}#谷歌刷新头
refresh-header-twoLevel = {group ="io.github.scwang90", name = "refresh-header-two-level",version.ref = "refreshLayout"}#二级刷新头
refresh-footer-classics = {group ="io.github.scwang90", name = "refresh-footer-classics",version.ref = "refreshLayout"} #经典加载
refresh-footer-ball = {group ="io.github.scwang90", name = "refresh-footer-ball",version.ref = "refreshLayout"}#球脉冲加载



[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }

#android-test = { id = "com.android.test", version.ref = "androidGradlePlugin" }
gradle-versions = { id = "com.github.ben-manes.versions", version.ref = "gradle-versions" }
#hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }

ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
spotless = { id = "com.diffplug.spotless", version.ref = "spotless" }
version-catalog-update = { id = "nl.littlerobots.version-catalog-update", version.ref = "version-catalog-update" }

